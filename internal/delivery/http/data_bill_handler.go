package http

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/delivery/http/request"
	"futures-asset/internal/delivery/http/response"
	"futures-asset/internal/domain"
	"futures-asset/internal/libs/pager"
	"futures-asset/pkg/eslib"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
)

func (handler *dataHandler) BillAsset(c *gin.Context) {
	var req payload.BillAssetParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	result := payload.BillAssetReply{Page: pager.Page{PageIndex: req.PageIndex, PageSize: req.PageSize}, Total: 0, List: make([]payload.BillAsset, 0)}

	conditions := make(map[string]interface{})
	terms := make(map[string][]interface{})
	if len(req.UID) > 0 {
		conditions["uid"] = req.UID
	}
	if len(req.Currency) > 0 {
		conditions["currency"] = strings.ToUpper(req.Currency)
	}
	if len(req.Symbol) > 0 {
		conditions["contractCode"] = strings.ToUpper(req.Symbol)
	}
	if req.BillType > 0 {
		conditions["billType"] = req.BillType
	} else {
		if req.Plat == domain.DepartmentOperate {
			terms["billType"] = []interface{}{domain.BillTypeFeeTrial, domain.BillTypeFundingTrial, domain.BillTypePlatFeeTrial, domain.BillTypeRealTrial, domain.BillTrialAssetRecycle}
		} else {
			terms["billType"] = []interface{}{
				domain.BillTypeFee, domain.BillTypeFeeTrial, domain.BillTypeFunding, domain.BillTypeFundingTrial, domain.BillTypePlatFee, domain.BillTypePlatFeeTrial,
				domain.BillTypeReal, domain.BillTypeRealTrial, domain.BillTrialAssetAdd, domain.BillTrialAssetRecycle, domain.BillTypeInnerIn, domain.BillTypeInnerOut, domain.BillTypeDeductProfitReal,
				domain.BillTypeDeductAdd,
			}
		}
	}
	// 如果搜索体验金
	if req.HaveTrial == 1 {
		terms["billType"] = []interface{}{domain.BillTypeFeeTrial, domain.BillTypeFundingTrial, domain.BillTypePlatFeeTrial, domain.BillTypeRealTrial, domain.BillTrialAssetRecycle, domain.BillTypeDeductProfitReal}
	}

	ranges := make(map[string]map[string]interface{})
	if req.StartTime > 0 {
		if _, ok := ranges["operateTime"]; !ok {
			ranges["operateTime"] = make(map[string]interface{})
		}
		ranges["operateTime"]["gte"] = req.StartTime * 1e9
	}
	if req.EndTime > 0 {
		if _, ok := ranges["operateTime"]; !ok {
			ranges["operateTime"] = make(map[string]interface{})
		}
		ranges["operateTime"]["lte"] = req.EndTime * 1e9
	}

	assetBill := eslib.New(domain.EsBillIndex).SetFrom((req.PageIndex - 1) * req.PageSize).SetSize(req.PageSize).SetConditions(conditions).
		SetTerms(terms).
		SetRanges(ranges).
		SetSortFields(
			map[string]bool{
				"operateTime": false,
			})

	searchResult, err := assetBill.Search(context.Background())
	if err != nil || searchResult == nil {
		logrus.Error(fmt.Sprintf("es search err:%+v, searchResult:%+v", err, searchResult))
		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrEsSearch, errors.New(domain.ErrMsg[domain.ErrEsSearch])))
		return
	}

	// 查询总量
	total := searchResult.TotalHits()
	if total > 0 {
		result.Total = total
		for _, hit := range searchResult.Hits.Hits {
			itemByte, err := hit.Source.MarshalJSON()
			if err != nil {
				continue
			}
			var item payload.BillAsset
			err = json.Unmarshal(itemByte, &item)
			if err != nil {
				continue
			}
			if !item.FundingRate.IsZero() {
				item.FundingRate = item.FundingRate.Mul(decimal.NewFromFloat(100))
			}

			item.MarkPrice = item.MarkPrice.Truncate(domain.CurrencyPrecision)
			item.OperateTime = item.OperateTime / 1e9
			result.List = append(result.List, item)
		}
	}

	c.JSON(http.StatusOK, result)
}
