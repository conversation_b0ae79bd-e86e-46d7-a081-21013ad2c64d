package domain

import "fmt"

const (
	DefaultBaseNum = 480                 // 资金费率计算取 DefaultBaseNum 次平均
	UBaseNum       = "fasset:base:num:u" // u本位基差计算周期
	BBaseNum       = "fasset:base:num:b" // b本位基差计算周期

	RedisFundRate = "fasset:fund:rate:"    // 资金费率
	RedisPIndex   = "fasset:fund:p_index:" // 资金费率
)

func GetFundRateRedisKey(_contractCode string) string {
	return AssetPrefix.Key(RedisFundRate + _contractCode)
}

func GetPIndexRedisKey(_contractCode string) string {
	return AssetPrefix.Key(RedisPIndex + _contractCode)
}

// GetPlatformIndexPriceRedisKey 平台指数价格 redis key
func GetPlatformIndexPriceRedisKey() string {
	return fmt.Sprintf("%s:index_price", MutexSwapPlatform)
}

// GetPlatformIndexPriceComposeInfoRedisKey 平台指数价格 redis key
func GetPlatformIndexPriceComposeInfoRedisKey() string {
	return fmt.Sprintf("%s:using_index_platform", MutexSwapPlatform)
}

// GetPlatformFundRateRedisKey 平台资金费率 redis key
func GetPlatformFundRateRedisKey() string {
	return fmt.Sprintf("%s:funding_rate", MutexSwapPlatform)
}

// 获取合约最新价格 redis key
func GetContractLastPriceRedisKey() string {
	return fmt.Sprintf("%s:last_price", MutexSwapPlatform)
}

// 获取标记价格可用标记 redis key
func GetUsingMarkPriceRedisKey() string {
	return fmt.Sprintf("%s:using_mark_platform", MutexSwapBurst)
}

// 获取合约指数原始信息redis key
func GetPlatformContractOriginIndexRedisKey(platformName string) string {
	return fmt.Sprintf("%s:%s:origin_index", MutexSwapBurst, platformName)
}

// 获取合约指数价格K线redis key
func GetContractPriceKlineRedisKey(accountType string, base string, quote string, klineType int64) string {
	return fmt.Sprintf("c_%s:kline_sec_zset:%s_%s_%d", accountType, base, quote, klineType)
}
